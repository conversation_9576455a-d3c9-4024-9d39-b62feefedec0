{"compilerOptions": {"target": "es2023", "module": "ESNext", "lib": ["es2023", "dom"], "skipLibCheck": true, "allowJs": true, "jsx": "react-jsx", "moduleResolution": "node", "baseUrl": "../", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noUnusedParameters": true, "paths": {"@cbsa/*": ["../src/cbsa-app/*"], "@common/*": ["../src/common-app/*"], "@redact/*": ["../src/redact-app/*"], "@resources/*": ["../resources/*"], "@helpers/*": ["../src/helpers/*"], "@utils": ["../src/utils/index"], "@i18n": ["../src/common-app/i18n/index"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["reports/**"]}